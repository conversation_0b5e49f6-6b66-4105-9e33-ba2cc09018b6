#!/usr/bin/env python3
"""
Telegram 会话生成器

此脚本帮助您生成 Telegram 会话字符串，可用于其他应用程序的身份验证，
无需每次都输入手机号码和验证码。

使用方法:
    python get_telegram_session.py

要求:
    - Telegram API 凭据 (api_id 和 api_hash) 从 https://my.telegram.org/apps 获取
    - 有效的 Telegram 账户和手机号码
"""

import os
import sys
from telethon import TelegramClient
from telethon.sessions import StringSession


def get_api_credentials():
    """从环境变量或用户输入获取 API 凭据。"""
    api_id = os.getenv('TELEGRAM_API_ID')
    api_hash = os.getenv('TELEGRAM_API_HASH')

    if not api_id:
        try:
            api_id = input("请输入您的 Telegram API ID (从 https://my.telegram.org/apps 获取): ").strip()
            if not api_id.isdigit():
                raise ValueError("API ID 必须是数字")
        except (KeyboardInterrupt, EOFError):
            print("\n操作已取消。")
            sys.exit(1)

    if not api_hash:
        try:
            api_hash = input("请输入您的 Telegram API Hash: ").strip()
            if not api_hash:
                raise ValueError("API Hash 不能为空")
        except (KeyboardInterrupt, EOFError):
            print("\n操作已取消。")
            sys.exit(1)

    return int(api_id), api_hash


def main():
    """生成 Telegram 会话的主函数。"""
    print("=== Telegram 会话生成器 ===")
    print("此工具将帮助您生成用于 Telegram 身份验证的会话字符串。")
    print("您需要从 https://my.telegram.org/apps 获取 API 凭据\n")

    # 配置选项
    save_to_file = True  # 是否将会话保存到文件
    session_name = 'telegram_session'  # 默认会话文件名

    try:
        # 获取 API 凭据
        api_id, api_hash = get_api_credentials()

        # 询问会话文件名
        user_session_name = input(f"请输入会话文件名 (默认: {session_name}): ").strip()
        if user_session_name:
            session_name = user_session_name

        # 创建 Telegram 客户端
        print("\n正在连接到 Telegram...")
        client = TelegramClient(StringSession(), api_id, api_hash)

        # 启动客户端 (这将提示输入手机号和验证码)
        print("请按照身份验证提示操作:")
        client.start()

        # 获取会话字符串
        session_string = client.session.save()

        # 如果需要，保存到文件
        if save_to_file:
            session_file = f"{session_name}.session"
            try:
                with open(session_file, 'w', encoding='utf-8') as f:
                    f.write(session_string)
                print(f"\n✅ 会话已保存到文件: {session_file}")
            except IOError as e:
                print(f"\n⚠️  警告: 无法保存到文件: {e}")

        # 显示会话字符串
        print("\n" + "="*60)
        print("🔑 您的 TELEGRAM 会话字符串:")
        print("="*60)
        print(session_string)
        print("="*60)

        print("\n📋 如何使用此会话字符串:")
        print("1. 安全保存此字符串 - 它提供对您 Telegram 账户的访问权限")
        print("2. 在应用程序中使用它，而不是手机号码身份验证")
        print("3. 在项目中将其设置为 SESSION_STRING 环境变量")
        print("4. 或直接使用保存的 .session 文件")

        print("\n⚠️  安全注意事项:")
        print("- 保持此会话字符串私密和安全")
        print("- 不要与他人分享")
        print("- 如果泄露请重新生成")
        print("- 遵守 Telegram 服务条款")

    except KeyboardInterrupt:
        print("\n\n用户取消了操作。")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 错误: {e}")
        print("请检查您的 API 凭据并重试。")
        sys.exit(1)
    finally:
        # 清理资源
        try:
            if 'client' in locals():
                client.disconnect()
        except:
            pass


if __name__ == "__main__":
    main()
