#!/usr/bin/env python3
"""
Telegram Session Generator

This script helps you generate a Telegram session string that can be used
for authentication in other applications without requiring phone number
and verification code each time.

Usage:
    python get_telegram_session.py

Requirements:
    - Telegram API credentials (api_id and api_hash) from https://my.telegram.org/apps
    - Active Telegram account with phone number
"""

import os
import sys
from telethon import TelegramClient, events
from telethon.sessions import StringSession


def get_api_credentials():
    """Get API credentials from environment variables or user input."""
    api_id = os.getenv('TELEGRAM_API_ID')
    api_hash = os.getenv('TELEGRAM_API_HASH')
    
    if not api_id:
        try:
            api_id = input("Enter your Telegram API ID (from https://my.telegram.org/apps): ").strip()
            if not api_id.isdigit():
                raise ValueError("API ID must be a number")
        except (KeyboardInterrupt, EOFError):
            print("\nOperation cancelled.")
            sys.exit(1)
    
    if not api_hash:
        try:
            api_hash = input("Enter your Telegram API Hash: ").strip()
            if not api_hash:
                raise ValueError("API Hash cannot be empty")
        except (KeyboardInterrupt, EOFError):
            print("\nOperation cancelled.")
            sys.exit(1)
    
    return int(api_id), api_hash


def main():
    """Main function to generate Telegram session."""
    print("=== Telegram Session Generator ===")
    print("This tool will help you generate a session string for Telegram authentication.")
    print("You'll need API credentials from https://my.telegram.org/apps\n")
    
    # Configuration
    save_to_file = True  # Whether to save session to file
    session_name = 'telegram_session'  # Default session file name
    
    try:
        # Get API credentials
        api_id, api_hash = get_api_credentials()
        
        # Ask for session file name
        user_session_name = input(f"Enter session file name (default: {session_name}): ").strip()
        if user_session_name:
            session_name = user_session_name
        
        # Create Telegram client
        print("\nConnecting to Telegram...")
        client = TelegramClient(StringSession(), api_id, api_hash)
        
        # Start the client (this will prompt for phone and verification code)
        print("Please follow the authentication prompts:")
        client.start()
        
        # Get the session string
        session_string = client.session.save()
        
        # Save to file if requested
        if save_to_file:
            session_file = f"{session_name}.session"
            try:
                with open(session_file, 'w', encoding='utf-8') as f:
                    f.write(session_string)
                print(f"\n✅ Session saved to file: {session_file}")
            except IOError as e:
                print(f"\n⚠️  Warning: Could not save to file: {e}")
        
        # Display the session string
        print("\n" + "="*60)
        print("🔑 YOUR TELEGRAM SESSION STRING:")
        print("="*60)
        print(session_string)
        print("="*60)
        
        print("\n📋 How to use this session string:")
        print("1. Save this string securely - it provides access to your Telegram account")
        print("2. Use it in your applications instead of phone number authentication")
        print("3. Set it as SESSION_STRING environment variable in your projects")
        print("4. Or use the saved .session file directly")
        
        print("\n⚠️  Security Notes:")
        print("- Keep this session string private and secure")
        print("- Don't share it with others")
        print("- Regenerate if compromised")
        print("- Follow Telegram's Terms of Service")
        
    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("Please check your API credentials and try again.")
        sys.exit(1)
    finally:
        # Clean up
        try:
            if 'client' in locals():
                client.disconnect()
        except:
            pass


if __name__ == "__main__":
    main()
