
import httpx

def get_proxy(max_number=10):
    while max_number > 0:
        try:
            # resp = httpx.get("http://api.ipweb.cc:8004/api/agent/account2?country=US&times=90&limit=1",
            #                  headers={"Token": "GJ7VJ0URV2AOSAUET5WSN8O7MB4GCKG0"})
            resp = httpx.get("http://api.ipweb.cc:8004/api/agent/account2?country=HK&times=90&limit=1",
                             headers={"Token": "GJ7VJ0URV2AOSAUET5WSN8O7MB4GCKG0"})
            username = resp.json()["data"][0].split(":")[0]
            password = resp.json()["data"][0].split(":")[1]
            proxy = {
                'proxy_type': 'http',  # (mandatory) protocol to use (see above)
                'addr': "gate1.ipweb.cc",  # (mandatory) proxy IP address
                'port': 7778,  # (mandatory) proxy port number
                'username': username,  # (optional) username if the proxy requires auth
                'password': password,  # (optional) password if the proxy requires auth
                'rdns': True  # (optional) whether to use remote or local resolve, default remote
            }
            # print (proxy)
            return proxy
        except (Exception,) as e:
            max_number -= 1
            # await asyncio.sleep(1)



if __name__ == '__main__':
    print(get_proxy(8))