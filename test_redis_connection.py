#!/usr/bin/env python3
"""
Redis 连接测试脚本

此脚本使用 aioredis 测试 Redis 连接，并执行基本的读写操作来验证连接是否正常。

使用方法:
    python test_redis_connection.py

要求:
    - Redis 服务器运行中
    - aioredis 库已安装
"""

import asyncio
import os
import sys
import time
from datetime import datetime
import aioredis
from aioredis import Redis


def get_redis_config():
    """从环境变量或默认值获取 Redis 配置。"""
    config = {
        'host': os.getenv('REDIS_HOST', 'localhost'),
        'port': int(os.getenv('REDIS_PORT', 6379)),
        'db': int(os.getenv('REDIS_DB', 0)),
        'password': os.getenv('REDIS_PASSWORD'),
        'username': os.getenv('REDIS_USERNAME'),
        'decode_responses': True,
        'socket_timeout': 5,
        'socket_connect_timeout': 5,
    }
    
    # 移除空值
    config = {k: v for k, v in config.items() if v is not None}
    
    return config


async def test_basic_connection(redis: Redis):
    """测试基本连接。"""
    print("🔗 测试基本连接...")
    try:
        # 测试 ping
        pong = await redis.ping()
        if pong:
            print("✅ PING 测试成功")
            return True
        else:
            print("❌ PING 测试失败")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False


async def test_basic_operations(redis: Redis):
    """测试基本的读写操作。"""
    print("\n📝 测试基本读写操作...")
    test_key = "test:connection"
    test_value = f"测试值_{int(time.time())}"
    
    try:
        # 设置值
        await redis.set(test_key, test_value, ex=60)  # 60秒过期
        print(f"✅ SET 操作成功: {test_key} = {test_value}")
        
        # 获取值
        retrieved_value = await redis.get(test_key)
        if retrieved_value == test_value:
            print(f"✅ GET 操作成功: {retrieved_value}")
        else:
            print(f"❌ GET 操作失败: 期望 {test_value}, 得到 {retrieved_value}")
            return False
        
        # 删除测试键
        await redis.delete(test_key)
        print("✅ DELETE 操作成功")
        
        return True
    except Exception as e:
        print(f"❌ 基本操作失败: {e}")
        return False


async def test_hash_operations(redis: Redis):
    """测试哈希操作。"""
    print("\n🗂️ 测试哈希操作...")
    hash_key = "test:hash"
    
    try:
        # 设置哈希字段
        await redis.hset(hash_key, mapping={
            "field1": "value1",
            "field2": "value2",
            "timestamp": str(datetime.now())
        })
        print("✅ HSET 操作成功")
        
        # 获取哈希字段
        field1_value = await redis.hget(hash_key, "field1")
        print(f"✅ HGET 操作成功: field1 = {field1_value}")
        
        # 获取所有哈希字段
        all_fields = await redis.hgetall(hash_key)
        print(f"✅ HGETALL 操作成功: {len(all_fields)} 个字段")
        
        # 清理
        await redis.delete(hash_key)
        print("✅ 哈希清理完成")
        
        return True
    except Exception as e:
        print(f"❌ 哈希操作失败: {e}")
        return False


async def test_list_operations(redis: Redis):
    """测试列表操作。"""
    print("\n📋 测试列表操作...")
    list_key = "test:list"
    
    try:
        # 推入列表
        await redis.lpush(list_key, "item1", "item2", "item3")
        print("✅ LPUSH 操作成功")
        
        # 获取列表长度
        length = await redis.llen(list_key)
        print(f"✅ LLEN 操作成功: 列表长度 = {length}")
        
        # 获取列表范围
        items = await redis.lrange(list_key, 0, -1)
        print(f"✅ LRANGE 操作成功: {items}")
        
        # 弹出元素
        popped = await redis.rpop(list_key)
        print(f"✅ RPOP 操作成功: 弹出 {popped}")
        
        # 清理
        await redis.delete(list_key)
        print("✅ 列表清理完成")
        
        return True
    except Exception as e:
        print(f"❌ 列表操作失败: {e}")
        return False


async def test_set_operations(redis: Redis):
    """测试集合操作。"""
    print("\n🎯 测试集合操作...")
    set_key = "test:set"
    
    try:
        # 添加集合成员
        await redis.sadd(set_key, "member1", "member2", "member3", "member1")  # member1重复
        print("✅ SADD 操作成功")
        
        # 获取集合大小
        size = await redis.scard(set_key)
        print(f"✅ SCARD 操作成功: 集合大小 = {size}")
        
        # 获取所有成员
        members = await redis.smembers(set_key)
        print(f"✅ SMEMBERS 操作成功: {members}")
        
        # 检查成员存在
        exists = await redis.sismember(set_key, "member1")
        print(f"✅ SISMEMBER 操作成功: member1 存在 = {exists}")
        
        # 清理
        await redis.delete(set_key)
        print("✅ 集合清理完成")
        
        return True
    except Exception as e:
        print(f"❌ 集合操作失败: {e}")
        return False


async def get_redis_info(redis: Redis):
    """获取 Redis 服务器信息。"""
    print("\n📊 Redis 服务器信息:")
    try:
        info = await redis.info()
        
        # 显示关键信息
        print(f"Redis 版本: {info.get('redis_version', 'N/A')}")
        print(f"运行模式: {info.get('redis_mode', 'N/A')}")
        print(f"已连接客户端: {info.get('connected_clients', 'N/A')}")
        print(f"使用内存: {info.get('used_memory_human', 'N/A')}")
        print(f"运行时间: {info.get('uptime_in_seconds', 'N/A')} 秒")
        
        return True
    except Exception as e:
        print(f"❌ 获取服务器信息失败: {e}")
        return False


async def main():
    """主测试函数。"""
    print("=== Redis 连接测试工具 ===")
    print("使用 aioredis 测试 Redis 连接和基本操作\n")
    
    # 获取配置
    config = get_redis_config()
    print("📋 Redis 配置:")
    for key, value in config.items():
        if key == 'password' and value:
            print(f"  {key}: {'*' * len(str(value))}")
        else:
            print(f"  {key}: {value}")
    print()
    
    redis = None
    try:
        # 创建 Redis 连接
        print("🚀 创建 Redis 连接...")
        redis = aioredis.from_url(
            f"redis://{config.get('username', '')}{'@' if config.get('username') else ''}"
            f"{config['host']}:{config['port']}/{config['db']}",
            password=config.get('password'),
            decode_responses=config['decode_responses'],
            socket_timeout=config['socket_timeout'],
            socket_connect_timeout=config['socket_connect_timeout']
        )
        
        # 执行测试
        tests = [
            ("基本连接", test_basic_connection),
            ("基本操作", test_basic_operations),
            ("哈希操作", test_hash_operations),
            ("列表操作", test_list_operations),
            ("集合操作", test_set_operations),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                result = await test_func(redis)
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name}测试异常: {e}")
                results.append((test_name, False))
        
        # 获取服务器信息
        await get_redis_info(redis)
        
        # 显示测试结果摘要
        print("\n" + "="*50)
        print("📈 测试结果摘要:")
        print("="*50)
        
        passed = 0
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总计: {passed}/{len(results)} 项测试通过")
        
        if passed == len(results):
            print("🎉 所有测试通过！Redis 连接正常。")
        else:
            print("⚠️ 部分测试失败，请检查 Redis 配置和服务状态。")
            
    except Exception as e:
        print(f"❌ 连接创建失败: {e}")
        print("\n💡 请检查:")
        print("1. Redis 服务是否正在运行")
        print("2. 连接配置是否正确")
        print("3. 网络连接是否正常")
        print("4. 防火墙设置是否允许连接")
        sys.exit(1)
        
    finally:
        # 关闭连接
        if redis:
            try:
                await redis.close()
                print("\n🔒 Redis 连接已关闭")
            except:
                pass


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n用户取消了测试。")
        sys.exit(1)
