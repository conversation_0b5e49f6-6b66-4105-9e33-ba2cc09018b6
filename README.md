# Telegram Session Generator

A simple Python script to generate Telegram session strings for authentication in applications without requiring phone number and verification code each time.

## Features

- 🔐 Generate reusable Telegram session strings
- 💾 Save session to file for easy reuse
- 🛡️ Secure handling of API credentials
- 📱 Interactive authentication process
- ⚡ Environment variable support

## Prerequisites

1. **Telegram Account**: You need an active Telegram account
2. **API Credentials**: Get your `api_id` and `api_hash` from [https://my.telegram.org/apps](https://my.telegram.org/apps)

### Getting API Credentials

1. Go to [https://my.telegram.org/apps](https://my.telegram.org/apps)
2. Log in with your Telegram account
3. Create a new application
4. Note down your `api_id` and `api_hash`

## Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Method 1: Interactive Mode
```bash
python get_telegram_session.py
```
The script will prompt you for:
- API ID
- API Hash
- Session file name (optional)
- Phone number (during authentication)
- Verification code (sent to your Telegram)
- Two-factor authentication password (if enabled)

### Method 2: Environment Variables
Set environment variables to avoid manual input:
```bash
# Windows
set TELEGRAM_API_ID=your_api_id
set TELEGRAM_API_HASH=your_api_hash
python get_telegram_session.py

# Linux/Mac
export TELEGRAM_API_ID=your_api_id
export TELEGRAM_API_HASH=your_api_hash
python get_telegram_session.py
```

## Output

The script will generate:
1. **Session String**: A long string that represents your authenticated session
2. **Session File**: A `.session` file containing the session string (optional)

### Example Output
```
=== Telegram Session Generator ===
Connecting to Telegram...
Please follow the authentication prompts:
Please enter your phone (or bot token): +**********
Please enter the code you received: 12345
Signed in successfully as user

✅ Session saved to file: telegram_session.session

============================================================
🔑 YOUR TELEGRAM SESSION STRING:
============================================================
1BVtsOKw8u7koN...NqHR5ehWjJ8N60z...
============================================================
```

## Using the Session String

### In Python Applications
```python
from telethon import TelegramClient
from telethon.sessions import StringSession

# Use the session string
session_string = "1BVtsOKw8u7koN...NqHR5ehWjJ8N60z..."
client = TelegramClient(StringSession(session_string), api_id, api_hash)
```

### As Environment Variable
```bash
# Set as environment variable
export SESSION_STRING="1BVtsOKw8u7koN...NqHR5ehWjJ8N60z..."

# Use in Docker
docker run -e SESSION_STRING="1BVtsOKw8u7koN...NqHR5ehWjJ8N60z..." your-app
```

### Using Session File
```python
from telethon import TelegramClient

# Use the session file directly
client = TelegramClient('telegram_session.session', api_id, api_hash)
```

## Security Considerations

⚠️ **Important Security Notes:**

- **Keep your session string private** - it provides full access to your Telegram account
- **Don't share session strings** with others
- **Regenerate sessions** if you suspect they're compromised
- **Follow Telegram's Terms of Service** to avoid account restrictions
- **Store session strings securely** (use environment variables or secure storage)

## Troubleshooting

### Common Issues

1. **"Invalid API ID/Hash"**
   - Verify your credentials from https://my.telegram.org/apps
   - Ensure API ID is a number and API Hash is correct

2. **"Phone number invalid"**
   - Include country code (e.g., +**********)
   - Use the same phone number associated with your Telegram account

3. **"Two-factor authentication required"**
   - Enter your 2FA password when prompted
   - If you forgot your 2FA password, recover it through Telegram first

4. **"Session file not created"**
   - Check write permissions in the current directory
   - Ensure the directory exists and is writable

## Dependencies

- `telethon>=1.24.0` - Telegram client library for Python

## License

This project is provided as-is for educational and personal use. Please comply with Telegram's Terms of Service when using this tool.
