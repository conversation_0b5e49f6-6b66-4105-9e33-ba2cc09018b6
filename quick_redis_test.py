#!/usr/bin/env python3
"""
快速 Redis 连接测试

简化版的 Redis 连接测试脚本，用于快速验证连接是否正常。
"""

import asyncio
import aioredis
import sys


async def quick_test():
    """快速测试 Redis 连接。"""
    print("🚀 快速 Redis 连接测试...")
    
    try:
        # 使用默认配置连接 Redis
        redis = aioredis.from_url("redis://localhost:6379", decode_responses=True)
        
        # 测试 PING
        pong = await redis.ping()
        if pong:
            print("✅ Redis 连接成功！")
        
        # 简单的读写测试
        await redis.set("test_key", "Hello Redis!", ex=10)
        value = await redis.get("test_key")
        print(f"✅ 读写测试成功: {value}")
        
        # 获取基本信息
        info = await redis.info("server")
        print(f"✅ Redis 版本: {info.get('redis_version', 'N/A')}")
        
        # 清理
        await redis.delete("test_key")
        await redis.close()
        
        print("🎉 所有测试通过！")
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print("\n💡 请确保:")
        print("1. Redis 服务正在运行 (redis-server)")
        print("2. Redis 监听在 localhost:6379")
        print("3. 没有密码保护或防火墙阻止")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(quick_test())
